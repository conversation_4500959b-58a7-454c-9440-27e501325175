<template>
  <div class="px-30 pt-7.5 h-full">
    <!-- 测试页面标题 -->
    <div class="text-center mb-10">
      <h1 class="text-4xl font-bold text-black dark:text-white mb-4">LinkedIn API 测试页面</h1>
      <p class="text-gray-600 dark:text-gray-400">输入 LinkedIn 用户名或 URL 进行分析测试</p>
    </div>

    <!-- 输入表单 -->
    <div class="max-w-2xl mx-auto mb-10">
      <div class="bg-white dark:bg-[#141415] rounded-2xl p-8 shadow-lg">
        <div class="space-y-6">
          <!-- 输入框 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              LinkedIn 用户名或 URL
            </label>
            <input
              v-model="inputValue"
              type="text"
              placeholder="例如: john-doe 或 https://linkedin.com/in/john-doe"
              class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg 
                     bg-white dark:bg-[#1a1a1b] text-black dark:text-white
                     focus:ring-2 focus:ring-blue-500 focus:border-transparent
                     placeholder-gray-400 dark:placeholder-gray-500"
              @keyup.enter="startAnalysis"
            />
          </div>

          <!-- 开始分析按钮 -->
          <button
            @click="startAnalysis"
            :disabled="!inputValue.trim() || loading"
            class="w-full py-3 px-6 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400
                   text-white font-medium rounded-lg transition-colors duration-200
                   disabled:cursor-not-allowed"
          >
            {{ loading ? '分析中...' : '开始分析' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Loading 模态框 -->
    <Loading 
      :visible="loading" 
      :data="thinking" 
      @update:visible="handleLoadingClose" 
    />

    <!-- 结果显示区域 -->
    <div v-if="finalResult && !loading" class="max-w-4xl mx-auto">
      <div class="bg-white dark:bg-[#141415] rounded-2xl p-8 shadow-lg">
        <h2 class="text-2xl font-bold text-black dark:text-white mb-6">分析结果</h2>
        
        <!-- 结果内容 -->
        <div class="prose dark:prose-invert max-w-none">
          <div class="whitespace-pre-wrap text-gray-800 dark:text-gray-200">
            {{ finalResult }}
          </div>
        </div>

        <!-- 重新分析按钮 -->
        <div class="mt-8 text-center">
          <button
            @click="resetAnalysis"
            class="py-2 px-6 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg 
                   transition-colors duration-200"
          >
            重新分析
          </button>
        </div>
      </div>
    </div>

    <!-- 错误显示 -->
    <div v-if="error && !loading" class="max-w-2xl mx-auto">
      <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-5 h-5 text-red-400">⚠️</div>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
              分析出错
            </h3>
            <div class="mt-2 text-sm text-red-700 dark:text-red-300">
              {{ error.message }}
            </div>
          </div>
        </div>
        
        <!-- 重试按钮 -->
        <div class="mt-4">
          <button
            @click="startAnalysis"
            class="py-2 px-4 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded 
                   transition-colors duration-200"
          >
            重试
          </button>
        </div>
      </div>
    </div>

    <!-- 使用说明 -->
    <div class="max-w-2xl mx-auto mt-10">
      <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
        <h3 class="text-lg font-medium text-blue-800 dark:text-blue-200 mb-3">使用说明</h3>
        <ul class="text-sm text-blue-700 dark:text-blue-300 space-y-2">
          <li>• 支持输入 LinkedIn 用户名（如：john-doe）</li>
          <li>• 支持输入完整的 LinkedIn URL（如：https://linkedin.com/in/john-doe）</li>
          <li>• 分析过程中会显示实时进度</li>
          <li>• 分析完成后会显示详细结果</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useEventStream } from '~/composables/useEventStream'
import Loading from '~/components/Loading/index.vue'

// 页面状态
const inputValue = ref('')
const finalResult = ref('')
const error = ref<Error | null>(null)

// 使用 EventStream composable
const { loading, thinking, finalResponse, connectWithObj } = useEventStream()

// 开始分析
const startAnalysis = async () => {
  if (!inputValue.value.trim()) {
    return
  }

  // 重置状态
  finalResult.value = ''
  error.value = null
  thinking.value = []

  try {
    // 使用 connectWithObj 方法发送请求
    await connectWithObj(
      { content: inputValue.value.trim() },
      '/api/linkedin/analyze',
      {}
    )
  } catch (err) {
    error.value = err as Error
    console.error('LinkedIn analysis error:', err)
  }
}

// 监听最终结果
watch(finalResponse, (newValue) => {
  if (newValue) {
    finalResult.value = newValue
  }
})

// 处理 Loading 模态框关闭
const handleLoadingClose = () => {
  // 如果用户手动关闭 Loading，停止当前分析
  loading.value = false
}

// 重置分析
const resetAnalysis = () => {
  inputValue.value = ''
  finalResult.value = ''
  error.value = null
  thinking.value = []
}

// 页面元数据
useHead({
  title: 'LinkedIn API 测试 - DINQ',
  meta: [
    { name: 'description', content: 'LinkedIn API 分析测试页面' }
  ]
})
</script>

<style scoped>
/* 自定义样式 */
.prose {
  max-width: none;
}

.prose pre {
  background-color: #f8f9fa;
  border-radius: 0.5rem;
  padding: 1rem;
  overflow-x: auto;
}

.dark .prose pre {
  background-color: #1a1a1b;
}
</style>
