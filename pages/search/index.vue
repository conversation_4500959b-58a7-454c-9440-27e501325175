<template>
  <div class="flex relative">
    <Sidebar v-model:active-page="activePage" @reset-content="handleResetContent" />
    <div class="flex-1">
      <!-- Main content here -->
      <component
        :is="currentContentComponent"
        :on-go-to-upload="() => (activePage = 'Upload')"
        @go-back="() => (activePage = 'Talents')"
        @switch-to-poachub="() => (activePage = 'Poachub')"
        @switch-to-results="handleSwitchToResults"
        @back-to-search="handleBackToSearch"
        :initial-cards="searchResultsData.cards"
        :initial-query="searchResultsData.query"
        :initial-filter="searchResultsData.filter"
        ref="contentRef"
        class="w-full h-full"
      />
    </div>
    <div class="fx-cer gap-3 desktop-actions absolute right-8 top-4 z-30">
      <a href="https://x.com/dinq_io" target="_blank" class="flex">
        <button class="media-btn">
          <img
            :src="currentTheme === 'dark' ? '/image/header-x-dark.svg' : '/image/header-x.svg'"
            alt="X (Twitter)"
            class="x-icon"
          />
        </button>
      </a>

      <button class="theme-btn" @click="toggleTheme">
        <div
          :class="currentTheme === 'light' ? 'i-carbon:sun' : 'i-carbon:moon'"
          class="text-base"
        ></div>
      </button>
      <button
        class="media-btn"
        v-if="currentUser"
        @click="handleLogout"
        style="display: none"
      ></button>
      <!-- 认证加载状态 -->
      <div v-if="!authInitialized" class="auth-loading">
        <div class="loading-spinner"></div>
      </div>

      <!-- 已登录用户下拉菜单 -->
      <UserDropdown
        v-else-if="currentUser"
        :user="{
          profilePicture: userProfile?.profile_picture,
          photoUrl: firebaseUserProfile?.photo_url || currentUser.photoURL,
          name:
            userProfile?.display_name ||
            firebaseUserProfile?.display_name ||
            currentUser.displayName,
          username: currentUser.username || currentUser.email || '',
          verified: currentUser.verified || true,
        }"
        :open="showUserDropdown"
        @update:open="showUserDropdown = $event"
        @signout="handleLogout"
        @settings="onSettings"
        @verification="onVerification"
      />

      <!-- 未登录用户登录按钮 -->
      <button class="login-btn" @click="showModal = true" v-else>Login</button>
    </div>
    <AuthModal
      ref="authModal"
      :visible="showModal"
      :loading="loading"
      :provider="currentProvider as 'google' | 'twitter'"
      @update:visible="showModal = $event"
      @auth="onAuth"
    />
  </div>
</template>

<script setup lang="ts">
  // 定义使用search布局
  definePageMeta({
    layout: 'search'
  })

  import Sidebar from './Sidebar.vue'
  import SearchPage from './SearchPage.vue' // 主搜索页面组件
  import SearchResultsPage from './SearchResultsPage.vue' // 搜索结果页面组件
  import TalentFavorites from './TalentFavorites.vue'
  import TransferNews from './TransferNews.vue'
  import UserDropdown from '@/components/UserDropdown.vue'
  import UploadPage from './UploadPage.vue'
  import SearchApiTest from './SearchApiTest.vue'
  import { ref, computed, defineProps } from 'vue'
  import { getCurrentUser, getCurrentFirebaseUser, updateUserInfo } from '@/api/user'
  const userProfile = ref<any>(null)
  const firebaseUserProfile = ref<any>(null)
  const router = useRouter()
  const { loading, currentUser, authInitialized, loginWithGoogle, logout } =
    useFirebaseAuth()
  const activePage = ref('Search')
  const contentRef = ref()
  const currentTheme = ref('light')

  // 搜索结果状态
  const searchResultsData = ref<{
    cards: any[]
    query: string
    filter: string
  }>({
    cards: [],
    query: '',
    filter: ''
  })
  const { $emitter } = useNuxtApp()
  const showModal = ref(false)
  const showUpgradeModal = ref(false)
  const showUserDropdown = ref(false)
  const activeTab = ref('settings')
  const upgradeRef = ref<HTMLElement | null>(null)
  const showSettingsModal = ref(false)
  const authModal = ref<ComponentPublicInstance<{ resetLoading: () => void }> | null>(null)
  const currentProvider = ref('')
  const isMobile = ref(false)
  onMounted(() => {
    isMobile.value = window.innerWidth < 768
  })

  // 切换主题
  const toggleTheme = () => {
    const newTheme = currentTheme.value === 'light' ? 'dark' : 'light'
    currentTheme.value = newTheme
    localStorage.setItem('theme', newTheme)
    applyTheme(newTheme)
    GLOBAL_CURRENT_THEME.value = newTheme
  }

  const handleClickOutside = event => {
    if (upgradeRef.value && !upgradeRef.value.contains(event.target)) {
      showUpgradeModal.value = false
    }
    // 同时处理用户下拉菜单的点击外部隐藏
    showUserDropdown.value = false
  }

  onMounted(() => {
    // 初始化主题
    const savedTheme = localStorage.getItem('theme')
    if (savedTheme) {
      currentTheme.value = savedTheme
    } else if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      currentTheme.value = 'dark'
    }
    applyTheme(currentTheme.value)

    // 事件监听
    $emitter.on('auth', () => {
      showModal.value = true
    })
    $emitter.on('user-profile-updated', () => {
      fetchUserProfile()
    })
    document.addEventListener('click', handleClickOutside)
  })

  const onAuth = async (provider: 'google' | 'twitter') => {
    currentProvider.value = provider
    switch (provider) {
      case 'google':
        await loginWithGoogle()
        break
      case 'twitter':
        break
      default:
        break
    }
    if (currentUser.value) {
      showModal.value = false
    }
    authModal.value?.resetLoading()
  }

  const applyTheme = theme => {
    const html = document.documentElement
    if (theme === 'dark') {
      html.classList.add('dark')
      html.setAttribute('data-theme', 'dark')
    } else {
      html.classList.remove('dark')
      html.setAttribute('data-theme', 'light')
    }
  }

  const currentContentComponent = computed(() => {
    switch (activePage.value) {
      case 'Search':
        return SearchPage
      case 'SearchResults':
        return SearchResultsPage
      case 'Talents':
        return TalentFavorites
      case 'Upload':
        return UploadPage
      case 'Poachub': // 修正为新的标签名
        return TransferNews
      case 'API Test':
        return SearchApiTest
      default:
        return null
    }
  })

  const handleResetContent = () => {
    // 如果当前在搜索结果页面，先返回搜索页面
    if (activePage.value === 'SearchResults') {
      handleBackToSearch()
    }
    contentRef.value?.resetData()
  }

  // 处理切换到搜索结果页面
  const handleSwitchToResults = (data: { cards: any[], query: string, filter: string }) => {
    searchResultsData.value = data
    activePage.value = 'SearchResults'
  }

  // 处理返回搜索页面
  const handleBackToSearch = () => {
    activePage.value = 'Search'
    // 清空搜索结果数据
    searchResultsData.value = {
      cards: [],
      query: '',
      filter: ''
    }
  }

  const fetchUserProfile = async () => {
    if (!currentUser.value?.uid) return

    try {
      const [userRes, firebaseRes] = await Promise.all([
        getCurrentUser({ Userid: currentUser.value.uid }),
        getCurrentFirebaseUser({ Userid: currentUser.value.uid }),
      ])

      if (userRes.success) {
        userProfile.value = userRes.user
      }
      if (firebaseRes.success) {
        firebaseUserProfile.value = firebaseRes.firebase_user
      }

      if (userRes.success && firebaseRes.success) {
        const updateData = {}

        if (
          (!userProfile.value?.display_name || userProfile.value.display_name === null) &&
          firebaseUserProfile.value?.display_name
        ) {
          updateData.display_name = firebaseUserProfile.value.display_name
        }

        if (
          (!userProfile.value?.email || userProfile.value.email === null) &&
          firebaseUserProfile.value?.email
        ) {
          updateData.email = firebaseUserProfile.value.email
        }

        if (Object.keys(updateData).length > 0) {
          try {
            const updateRes = await updateUserInfo(updateData, {
              Userid: currentUser.value.uid,
            })

            if (updateRes.success) {
              if (updateData.display_name) {
                userProfile.value.display_name = updateData.display_name
                console.log('Auto-synced display_name from Firebase:', updateData.display_name)
              }
              if (updateData.email) {
                userProfile.value.email = updateData.email
                console.log('Auto-synced email from Firebase:', updateData.email)
              }
            }
          } catch (error) {
            console.error('Error auto-syncing user info:', error)
          }
        }
      }
    } catch (error) {
      console.error('Error fetching user profile:', error)
    }
  }

  watch(
    () => currentUser.value?.uid,
    newUid => {
      if (newUid) {
        fetchUserProfile()
      } else {
        userProfile.value = null
        firebaseUserProfile.value = null
      }
    },
    { immediate: true }
  )

  function onSettings() {
    activeTab.value = 'settings'
    showSettingsModal.value = true
    // 通知导航栏降低z-index
    $emitter.emit('settings-modal', true)
  }

  const handleLogout = () => {
    logout()
    router.push('/analysis')
  }

  function onVerification() {
    activeTab.value = 'verification'
    showSettingsModal.value = true
    // 通知导航栏降低z-index
    $emitter.emit('settings-modal', true)
  }
</script>

<style scoped>
  @media (max-width: 768px) {
    .desktop-nav {
      display: none;
    }

    .desktop-actions {
      display: none;
    }

    .mobile-menu-btn {
      display: flex;
    }
  }

  .theme-btn {
    @apply rounded-full transition-colors bg-transparent text-gray-500 dark:bg-transparent dark:text-gray-400;
    display: flex;
    align-items: center;
    width: 42px;
    height: 42px;
    justify-content: center;
  }

  .theme-btn:hover {
    background-color: #f8f9fa;
    color: #cb7c5d;
  }

  .dark .theme-btn:hover {
    background-color: #353535;
    color: #cb7c5d;
  }

  .text-base {
    background-size: 100%;
    width: 30px;
    height: 30px;
    display: inline-block;
    padding: 4px;
  }

  .login-btn {
    @apply bg-white rounded-full transition-all;
    width: 120px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: Poppins;
    font-weight: 500;
    font-size: 16px;
    line-height: 160%;
    letter-spacing: 0%;
    text-align: center;
    color: #000000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .login-btn:hover {
    background-color: #f8f9fa;
  }

  .login-btn:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .dark .login-btn {
    @apply bg-[#2A2A2A];
    color: #e3e3e3;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .dark .login-btn:hover {
    background-color: #353535;
  }

  .dark .login-btn:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  }

  .media-btn {
    @apply rounded-full transition-colors text-gray-500 dark:text-gray-400;
    display: flex;
    align-items: center;
    width: 42px;
    height: 42px;
    justify-content: center;
  }

  .media-btn:hover {
    background-color: #f8f9fa;
    color: #cb7c5d;
  }

  .dark .media-btn:hover {
    background-color: #353535;
    color: #cb7c5d;
  }

  .x-icon {
    width: 20px;
    height: 20px;
  }

  .dark .x-icon {
    filter: brightness(0) saturate(100%) invert(67%) sepia(8%) saturate(434%) hue-rotate(183deg) brightness(90%) contrast(89%);
  }

  .media-btn:hover .x-icon {
    filter: brightness(0) saturate(100%) invert(56%) sepia(36%) saturate(614%) hue-rotate(336deg) brightness(92%) contrast(85%);
  }
</style>
