<template>
  <div>
    <!-- 遮罩层，仅在移动端展开状态下显示 -->
    <div
      v-if="isMobile && !isCollapsed"
      class="fixed inset-0 bg-black bg-opacity-50 z-30"
      @click="toggleCollapse"
    ></div>

    <div v-if="isMobile && isCollapsed">
      <button
        @click="toggleCollapse"
        class="absolute top-6 left-6 z-99 text-gray-500 hover:text-black dark:hover:text-white bg-transparent transition-colors"
      >
        <img src="~/assets/image/foldicon.png" alt="Collapse" class="w-6 h-6 btn-icon-light" />
        <img src="~/assets/image/foldIcon-d.svg" alt="Collapse" class="w-6 h-6 btn-icon-dark" />
      </button>
    </div>

    <!-- 侧边栏 -->
    <div
      class="h-screen bg-[#F8F8F8] border-r border-[#D6D6D6] dark:border-none transition-all dark:transition-all duration-300 flex flex-col justify-between dark:bg-[#2F3031]"
      :class="[
        isCollapsed
          ? isMobile
            ? 'w-0 relative'
            : 'w-16'
          : isMobile
            ? 'w-64 fixed top-0 left-0 z-40 shadow-xl'
            : 'w-64',
      ]"
    >
      <div>
        <!-- Logo & 折叠按钮 -->
        <div
          class="flex items-center p-4"
          :class="{ 'justify-center': isCollapsed, 'justify-between': !isCollapsed }"
        >
          <span v-if="!isCollapsed" @click="navigateToHome" class="cursor-pointer">
            <img
              :src="isDark ? '/image/darklogo2.png' : '/image/newlogo1.png'"
              width="60"
              height="30"
              :alt="isDark ? 'DINQ dark logo' : 'DINQ logo'"
            />
          </span>
          <button @click="toggleCollapse" class="text-gray-500 hover:text-black dark:hover:text-white bg-transparent transition-colors">
            <img src="~/assets/image/foldicon.png" alt="Collapse" class="btn-icon-light" />
            <img src="~/assets/image/foldIcon-d.svg" alt="Collapse" class="btn-icon-dark" />
          </button>
        </div>

        <div>
          <!-- 导航按钮 -->
          <div
            :class="{
              hidden: isMobile && isCollapsed,
              'flex flex-col px-2 gap-1': !isMobile || !isCollapsed,
            }"
          >
            <button
              v-for="item in navItems"
              :key="item.label"
              @click="
                () => {
                  setActivePage(item.label)
                  if (isMobile) isCollapsed = true
                }
              "
              class="flex items-center gap-2 h-10 rounded-2 hover:bg-gray-200 font-100 dark:hover:bg-gray-200/20"
              :class="{
                'bg-[#FFFFFF] font-semibold border-[#E8E8E8] dark:bg-[#3D3F40]':
                  props.activePage === item.label,
                'bg-transparent font-semibold': props.activePage !== item.label,
                'justify-center': isCollapsed,
                'px-2': !isCollapsed,
              }"
            >
              <img
                :src="isDark ? item.iconDark : item.iconLight"
                alt="icon"
                class="w-6 h-6"
                :class="{
                  'rounded w-8 h-8 p-1': isCollapsed,
                  'search-icon': item.label === 'Search' && !isCollapsed,
                  'search-icon-collapsed': item.label === 'Search' && isCollapsed,
                  'sidebar-icon-dark': (item.label === 'Talents' || item.label === 'Poachub'),
                }"
              />
              <span v-if="!isCollapsed" class="font-400">{{ item.label }}</span>
            </button>
          </div>
        </div>
      </div>
      <!-- 底部 Credits & 升级按钮 -->
      <div class="p-4">
        <div v-if="!isCollapsed" class="mb-4 text-sm text-gray-700 dark:text-gray-300">
          <div class="mb-1">Credits : {{ creditsDisplay }}</div>
          <div class="w-full h-1 bg-gray-200 dark:bg-gray-600 rounded">
            <div class="h-full bg-black dark:bg-gray-200 rounded" :style="{ width: creditsPercentage }"></div>
          </div>
          <div class="text-xs text-gray-400 dark:text-gray-500 mt-1">{{ creditsDescription }}</div>
        </div>

        <button
          @click="navigateToSubscription"
          :class="isCollapsed
            ? 'flex items-center gap-2 h-10 rounded-2 hover:bg-gray-200 font-100 dark:hover:bg-gray-200/20 bg-transparent font-semibold justify-center'
            : 'w-full h-9 bg-black text-white rounded hover:bg-gray-800 dark:bg-[#3D3F40] dark:hover:bg-[#4A4D4F] transition-colors'"
        >
          <span class="flex items-center justify-center gap-2">
            <img
              v-if="isCollapsed"
              src="/image/upgrade-hide.png"
              alt="Upgrade"
              class="w-8 h-8 dark:brightness-0 dark:invert"
            />
            <img
              v-else
              :src="Arrowup"
              alt="Upgrade"
              class="w-6 h-6"
            />
            <span v-if="!isCollapsed">Upgrade</span>
          </span>
        </button>

        <div
          v-if="!isCollapsed && isMobile"
          class="fx-cer gap-3 desktop-actions justify-between mt-2"
        >
          <button class="theme-btn rounded-full p-2" @click="toggleTheme">
            <div
              :class="currentTheme === 'light' ? 'i-carbon:sun' : 'i-carbon:moon'"
              class="text-base w-6 h-6"
            ></div>
          </button>

          <a href="https://x.com/dinq_io" target="_blank" class="flex">
            <button class="media-btn">
              <img
                :src="currentTheme === 'dark' ? '/image/header-x-dark.svg' : '/image/header-x.svg'"
                alt="X (Twitter)"
                class="x-icon rounded-full p-2"
              />
            </button>
          </a>

          <button
            class="media-btn"
            v-if="currentUser"
            @click="handleLogout"
            style="display: none"
          ></button>
          <!-- 认证加载状态 -->
          <div v-if="!authInitialized" class="auth-loading">
            <div class="loading-spinner"></div>
          </div>

          <!-- 已登录用户下拉菜单 -->
          <UserDropdown
            v-else-if="currentUser"
            :user="{
              profilePicture: userProfile?.profile_picture,
              photoUrl: firebaseUserProfile?.photo_url || currentUser.photoURL,
              name:
                userProfile?.display_name ||
                firebaseUserProfile?.display_name ||
                currentUser.displayName,
              username: currentUser.username || currentUser.email || '',
              verified: currentUser.verified || true,
            }"
            :open="showUserDropdown"
            @update:open="showUserDropdown = $event"
            @signout="handleLogout"
            @click="showSetting"
          />

          <!-- 未登录用户登录按钮 -->
          <button class="login-btn" @click="showModal = true" v-else>Login</button>
        </div>
      </div>
    </div>
    <AuthModal
      ref="authModal"
      :visible="showModal"
      :loading="loading"
      :provider="currentProvider as 'google' | 'twitter'"
      @update:visible="showModal = $event"
      @auth="onAuth"
    />
    <MobileSetting v-model:show="showSettingsModal" />
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
  import { defineEmits } from 'vue'
  import MobileSetting from '@/components/MobileSetting/index.vue'

  import Plus from '~/assets/image/Plus2.svg'
  import Plus2 from '~/assets/image/Plus-w.svg'
  import favorites from '~/assets/image/star.png'
  import favorites2 from '~/assets/image/Star-dark.svg'
  import Arroww from '~/assets/image/Arrow-circle-up2.svg'
  import Arrowup from '~/assets/image/Arrow-circle-up.svg'
  import News from '~/assets/image/window1.svg'
  import News2 from '~/assets/image/window2.svg'
  import TestIcon from '~/assets/image/analysis.svg'
  import TestIcon2 from '~/assets/image/analysis2.svg'
  import SearchIcon from '/image/search-logo.svg'
  import SearchIconDark from '/image/search-logo-dark.svg'
  // 定义 props
  const props = defineProps<{
    activePage: string
  }>()

  const emit = defineEmits(['update:activePage', 'reset-content'])
  const isCollapsed = ref(false)
  const isSwitchingPage = ref(false)
  const isDark = ref(false)
  const isMobile = ref(false)
  const currentTheme = ref('light')
  const { $emitter } = useNuxtApp()
  const showModal = ref(false)
  const showUpgradeModal = ref(false)
  const showUserDropdown = ref(false)
  const activeTab = ref('settings')
  const upgradeRef = ref<HTMLElement | null>(null)
  const showSettingsModal = ref(false)
  const authModal = ref<ComponentPublicInstance<{ resetLoading: () => void }> | null>(null)
  const currentProvider = ref('')
  const { loading, currentUser, authInitialized, loginWithGoogle, logout } =
    useFirebaseAuth()
  import { getCurrentUser, getCurrentFirebaseUser, updateUserInfo } from '@/api/user'
  import { getCredits } from '@/api'
  const router = useRouter()
  const userProfile = ref<any>(null)
  const firebaseUserProfile = ref<any>(null)

  // Credits 相关状态
  const creditsData = ref<{ remaining: number; total: number } | null>(null)
  const creditsLoading = ref(false)

  const fetchUserProfile = async () => {
    if (!currentUser.value?.uid) return

    try {
      const [userRes, firebaseRes] = await Promise.all([
        getCurrentUser({ Userid: currentUser.value.uid }),
        getCurrentFirebaseUser({ Userid: currentUser.value.uid }),
      ])

      if (userRes.success) {
        userProfile.value = userRes.user
      }
      if (firebaseRes.success) {
        firebaseUserProfile.value = firebaseRes.firebase_user
      }

      if (userRes.success && firebaseRes.success) {
        const updateData = {}

        if (
          (!userProfile.value?.display_name || userProfile.value.display_name === null) &&
          firebaseUserProfile.value?.display_name
        ) {
          updateData.display_name = firebaseUserProfile.value.display_name
        }

        if (
          (!userProfile.value?.email || userProfile.value.email === null) &&
          firebaseUserProfile.value?.email
        ) {
          updateData.email = firebaseUserProfile.value.email
        }

        if (Object.keys(updateData).length > 0) {
          try {
            const updateRes = await updateUserInfo(updateData, {
              Userid: currentUser.value.uid,
            })

            if (updateRes.success) {
              if (updateData.display_name) {
                userProfile.value.display_name = updateData.display_name
                console.log('Auto-synced display_name from Firebase:', updateData.display_name)
              }
              if (updateData.email) {
                userProfile.value.email = updateData.email
                console.log('Auto-synced email from Firebase:', updateData.email)
              }
            }
          } catch (error) {
            console.error('Error auto-syncing user info:', error)
          }
        }
      }
    } catch (error) {
      console.error('Error fetching user profile:', error)
    }
  }

  const showSetting = () => {
    showSettingsModal.value = true
    toggleCollapse()
  }

  watch(
    () => currentUser.value?.uid,
    newUid => {
      if (newUid) {
        fetchUserProfile()
        fetchCredits() // 获取用户登录后的credits信息
      } else {
        userProfile.value = null
        firebaseUserProfile.value = null
        creditsData.value = null // 清除credits数据
      }
    },
    { immediate: true }
  )

  // 获取Credits数据
  const fetchCredits = async () => {
    if (!currentUser.value?.uid) return

    creditsLoading.value = true
    try {
      const response = await getCredits(currentUser.value.uid)
      if (response.data) {
        creditsData.value = response.data
        console.log('Credits data fetched:', response.data)
      }
    } catch (error) {
      console.error('Error fetching credits:', error)
      // 如果获取失败，使用默认值
      creditsData.value = { remaining: 5, total: 5 }
    } finally {
      creditsLoading.value = false
    }
  }

  // Credits显示计算属性
  const creditsDisplay = computed(() => {
    if (!currentUser.value) return '--/--' // 未登录
    if (!creditsData.value) return '--/--' // 无数据
    return `${creditsData.value.remaining}/${creditsData.value.total}`
  })

  // Credits百分比计算属性
  const creditsPercentage = computed(() => {
    if (!currentUser.value || !creditsData.value || creditsData.value.total === 0) return '0%'
    const percentage = (creditsData.value.remaining / creditsData.value.total) * 100
    return `${Math.max(0, Math.min(100, percentage))}%`
  })

  // Credits描述计算属性
  const creditsDescription = computed(() => {
    if (!currentUser.value) return 'Please login to view credits'
    return 'Monthly Credits for free Users'
  })

  const handleLogout = () => {
    logout()
    router.push('/analysis')
  }

  const navigateToSubscription = () => {
    router.push('/subscription')
  }

  const navigateToHome = () => {
    router.push('/')
  }

  const checkIsMobile = () => {
    isMobile.value = window.innerWidth < 768
  }

  onMounted(() => {
    checkIsMobile()
    window.addEventListener('resize', checkIsMobile)

    isDark.value = document.documentElement.classList.contains('dark')
    const observer = new MutationObserver(() => {
      isDark.value = document.documentElement.classList.contains('dark')
    })
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] })
  })

  onUnmounted(() => {
    window.removeEventListener('resize', checkIsMobile)
  })

  const toggleCollapse = () => {
    isCollapsed.value = !isCollapsed.value
    if (!isCollapsed.value) {
      showSettingsModal.value = false
    }
  }

  const navItems = [
    { label: 'Search', id: 'search', iconLight: SearchIcon, iconDark: SearchIconDark },
    { label: 'Talents', id: '2', iconLight: '/image/favorite.svg', iconDark: '/image/favorite.svg' },
    { label: 'Poachub', id: '3', iconLight: '/image/poachub.svg', iconDark: '/image/poachub.svg' },
  ]

  const setActivePage = label => {
    if (props.activePage !== label) {
      isSwitchingPage.value = true
    } else {
      isSwitchingPage.value = false
    }

    emit('update:activePage', label)

    // 如果点击 Search，无论当前在哪个页面都重置内容
    if (label === 'Search') {
      emit('reset-content')
    }
  }

  // 切换主题
  const toggleTheme = () => {
    const newTheme = currentTheme.value === 'light' ? 'dark' : 'light'
    currentTheme.value = newTheme
    localStorage.setItem('theme', newTheme)
    applyTheme(newTheme)
    GLOBAL_CURRENT_THEME.value = newTheme
  }

  const handleClickOutside = event => {
    if (upgradeRef.value && !upgradeRef.value.contains(event.target)) {
      showUpgradeModal.value = false
    }
    // 同时处理用户下拉菜单的点击外部隐藏
    showUserDropdown.value = false
  }

  onMounted(() => {
    // 初始化主题
    const savedTheme = localStorage.getItem('theme')
    if (savedTheme) {
      currentTheme.value = savedTheme
    } else if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      currentTheme.value = 'dark'
    }
    applyTheme(currentTheme.value)

    // 检查用户是否已经登录并获取credits
    if (currentUser.value?.uid) {
      fetchUserProfile()
      fetchCredits()
    }

    // 事件监听
    $emitter.on('auth', () => {
      showModal.value = true
    })
    $emitter.on('user-profile-updated', () => {
      fetchUserProfile()
    })
    $emitter.on('search-success', () => {
      // search成功后更新credits
      fetchCredits()
    })
    document.addEventListener('click', handleClickOutside)
  })

  const onAuth = async (provider: 'google' | 'twitter') => {
    currentProvider.value = provider
    switch (provider) {
      case 'google':
        await loginWithGoogle()
        break
      case 'twitter':
        break
      default:
        break
    }
    if (currentUser.value) {
      showModal.value = false
    }
    authModal.value?.resetLoading()
  }

  const applyTheme = theme => {
    const html = document.documentElement
    if (theme === 'dark') {
      html.classList.add('dark')
      html.setAttribute('data-theme', 'dark')
    } else {
      html.classList.remove('dark')
      html.setAttribute('data-theme', 'light')
    }
  }
</script>

<style scoped>
  /* 图标切换 */
  .btn-icon-dark {
    display: none;
  }
  .dark .btn-icon-light {
    display: none;
  }
  .dark .btn-icon-dark {
    display: inline-block;
  }

  /* Search图标圆角 */
  .search-icon {
    border-radius: 8px;
    overflow: hidden;
    aspect-ratio: 1 / 1;
    object-fit: cover;
    transform: scale(1) translateX(5%);
  }

  /* Search图标折叠状态 */
  .search-icon-collapsed {
    border-radius: 8px;
    overflow: hidden;
    aspect-ratio: 1 / 1;
    object-fit: cover;
    transform: scale(1) translateX(10%);
  }

  .login-btn {
    @apply bg-white rounded-full transition-all;
    width: 120px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: Poppins;
    font-weight: 500;
    font-size: 16px;
    line-height: 160%;
    letter-spacing: 0%;
    text-align: center;
    color: #000000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .login-btn:hover {
    background-color: #f8f9fa;
  }

  .login-btn:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .dark .login-btn {
    @apply bg-[#2A2A2A];
    color: #e3e3e3;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .dark .login-btn:hover {
    background-color: #353535;
  }

  .dark .login-btn:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
  }

  /* Sidebar图标深色模式样式 */
  .dark .sidebar-icon-dark {
    filter: brightness(0) invert(1);
  }
</style>
