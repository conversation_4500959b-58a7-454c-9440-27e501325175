<template>
  <div v-if="show" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
    <div class="relative">
      <div
        class="rounded-2xl shadow-xl p-6 w-[1200px] h-[800px] bg-gradient-to-b from-[#FFFFFF] to-[#F4F2F1] dark:from-[#141415] dark:to-[#141415] border border-transparent dark:border-[#27282D]"
        data-card-id="share-card-github"
      >
        <div
          class="h-[740px] bg-[url(/image/graphbg.png)] bg-right bg-center bg-no-repeat bg-[length:700px_100%]"
        >
          <div class="w-[850px]">
            <!-- User Info -->
            <div class="flex items-center justify-start h-[80px]" v-if="user">
              <img :src="user.avatar" class="w-20 h-20 rounded-full mr-4" />
              <div class="flex flex-col flex-1 justify-around">
                <div class="flex items-center gap-4">
                  <h2 class="text-xl font-bold whitespace-nowrap truncate max-w-[300px]">
                    {{ user.name }}
                  </h2>
                  <div class="flex items-start gap-1 text-sm text-gray-500 flex-1">
                    <SvgIcon name="verified" class="mt-0.5 flex-shrink-0" />
                    <span class="line-clamp-2">{{ user.bio || user.role }}</span>
                  </div>
                </div>
                <div class="flex items-center" v-if="user?.login">
                  <a
                    :href="`https://github.com/${user.login}`"
                    target="_blank"
                    class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm transition-colors"
                  >
                    www.github.com/{{ user.login }}
                  </a>
                </div>
              </div>
            </div>

            <!-- main content -->
            <div class="flex items-center justify-between mt-6 gap-4">
              <!-- left -->
              <div class="fx-cer flex-col gap-2">
                <!-- career -->
                <div
                  class="flex flex-col justify-between p-4 flex-1 h-[325px] w-[450px] border border-gray-200 dark:border-[#27282D] bg-[#FFFFFF]/40 dark:bg-[#14141580] shadow-lg"
                  style="backdrop-filter: blur(14px); border-radius: 15px"
                >
                  <div>
                    <div class="fx-cer font-700 gap-2 mb-4"><SvgIcon name="goal" />Career</div>
                    <div
                      class="border-l-4 border-[#CB7C5D] dark:border-[#654D43] bg-[#FAF2EF] px-2 py-3 rounded-1 dark:bg-[#292929]"
                    >
                      <div
                        class="text-3.5 font-700 border-b-1 border-[#F2E8E4] dark:border-[#3E3E3E] text-[#2C2C2C] leading-6 pb-2 fx-cer gap-2 text-3.5 dark:text-white"
                      >
                        <SvgIcon name="promotion" /> Future Development Potential
                      </div>
                      <div
                        class="text-3.5 font-400 leading-6 text-[#4D4846] pt-2 dark:text-[#C6C6C6] line-clamp-2"
                      >
                        <!-- {{ githubData.role_model?.achievement || 'GitHub Developer' }} -->
                        High-performance lightweight Transformer architecture implementation
                        designed for AI applications on edge devices, providing ultra-low latency
                        and high accuracy.
                      </div>
                    </div>
                    <div class="mt-4">
                      <div
                        class="text-3.5 font-700 border-b-1 border-[#F2E8E4] dark:border-[#3E3E3E] text-[#2C2C2C] leading-6 pb-3 fx-cer gap-2 text-3.5 dark:text-[#C6C6C6]"
                      >
                        <SvgIcon name="chat-bubble" /> Development Advice & Evaluation
                      </div>
                      <div class="relative h-4">
                        <span
                          class="absolute w-25 border border-[#C88D75] top-[-2px] left-0"
                        ></span>
                        <span class="block w-full border-t-1 border-[#ECECEC] dark:border-gray-100/20"></span>
                      </div>

                      <div
                        class="text-3.5 leading-6 text-[#464646] font-400 dark:text-[#7E7E7E] leading-3.5 line-clamp-3"
                      >
                        <span class="dark:text-[#C6C6C6] font-600">Past Evaluation: </span>Specialized in data-driven recruitment, driving
                        high-potential talent acquisition at Blue Ops and demonstrating
                        cross-platform resource integration through Xerxes Global collaboration.
                      </div>
                    </div>
                  </div>
                </div>
                <!-- Life & Well-being -->
                <div
                  class="flex flex-col justify-between p-4 flex-1 h-[245px] w-[450px] border border-gray-200 dark:border-[#27282D] bg-[#FFFFFF]/40 dark:bg-[#14141580] shadow-lg"
                  style="backdrop-filter: blur(14px); border-radius: 15px"
                >
                  <div>
                    <div class="fx-cer font-700 gap-2 mb-2">
                      <SvgIcon name="healthcare" />Life & Well-being
                    </div>
                    <div
                      class="text-3.5 font-400 leading-5 text-[#2C2C2C] dark:text-[#D7D7D7] line-clamp-2"
                    >
                      <span class="text-3.5 font-700">Life suggestion：</span>Prioritize work-life
                      balance by setting clear boundaries. incorporate hobbies or socialactivities
                    </div>
                    <div class="w-full fx-cer justify-between border-b-1 border-[#ECECEC] dark:border-gray-100/20">
                      <div class="h-8 fx-cer gap-2 justify-between rounded">
                        <span>&#x1F3C0;</span>
                        <span class="text-3 text-[#3C3C3C] dark:text-gray-300"
                          >Set Boundaries;</span
                        >
                      </div>
                      <div class="h-8 fx-cer gap-2 justify-between rounded">
                        <span>&#x1F3C0;</span>
                        <span class="text-3 text-[#3C3C3C] dark:text-gray-300"
                          >Set Boundaries;</span
                        >
                      </div>
                      <div class="h-8 fx-cer gap-2 justify-between rounded">
                        <span>&#x1F3C0;</span>
                        <span class="text-3 text-[#3C3C3C] dark:text-gray-300"
                          >Set Boundaries;</span
                        >
                      </div>
                    </div>
                    <div
                      class="text-3.5 font-400 leading-5 text-[#2C2C2C] pt-2 dark:text-[#D7D7D7] line-clamp-2"
                    >
                      <span class="text-3.5 font-700">Health：</span>Schedule regular breaks during
                      long recruitment drives, Engage in 30-minute daily exercise. and ensure 7-8
                      hours of sleep for sustained energy.
                    </div>
                    <div class="w-full fx-cer justify-between">
                      <div class="h-8 fx-cer gap-2 justify-between rounded">
                        <span>&#x1F3C0;</span>
                        <span class="text-3 text-[#3C3C3C] dark:text-gray-300"
                          >Set Boundaries;</span
                        >
                      </div>
                      <div class="h-8 fx-cer gap-2 justify-between rounded">
                        <span>&#x1F3C0;</span>
                        <span class="text-3 text-[#3C3C3C] dark:text-gray-300"
                          >Set Boundaries;</span
                        >
                      </div>
                      <div class="h-8 fx-cer gap-2 justify-between rounded">
                        <span>&#x1F3C0;</span>
                        <span class="text-3 text-[#3C3C3C] dark:text-gray-300"
                          >Set Boundaries;</span
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- right -->
              <div class="fx-cer flex-col gap-3">
                <!-- salary -->
                <div
                  class="flex flex-col justify-between h-[155px] w-[385px] p-4 flex-1 border bg-[#FFFFFF]/40 border-gray-200 dark:border-[#27282D] dark:bg-[#14141580] shadow-lg"
                  style="backdrop-filter: blur(14px); border-radius: 15px"
                >
                  <div>
                    <div class="fx-cer font-700 gap-2 mb-4">
                      <SvgIcon name="wallet" />Estimated Salary
                    </div>
                    <div class="fx-cer flex-col justify-center">
                      <div class="text-11 text-black font-600 dark:text-white">300 K ~ 400 K</div>
                      <div class="text-[#7c7c7c] text-3.5 font-400 dark:text-[#7A7A7A]">Estimated Annual Earnings</div>
                    </div>
                  </div>
                </div>
                <!-- role model -->
                <div
                  class="flex flex-col justify-between h-[415px] w-[385px] p-4 flex-1 border bg-[#FFFFFF]/40 border-gray-200 dark:border-[#27282D] dark:bg-[#14141580] shadow-lg"
                  style="backdrop-filter: blur(14px); border-radius: 15px"
                >
                  <div>
                    <div class="fx-cer font-700 gap-2 mb-4">
                      <SvgIcon name="settings" />Role Model
                    </div>
                  </div>
                  <div>
                    <div class="flex flex-col h-full space-y-4">
                      <div class="fx-cer gap-3 w-full">
                        <img :src="user?.avatar" class="w-12 h-12 rounded-full" />
                        <div class="flex flex-col gap-1 flex-1">
                          <div class="text-3.5 font-bold fx-cer justify-between">
                            <span>{{ user?.name }}</span>
                          </div>
                          <div class="fx-cer gap-1.5 text-sm font-400 text-[#7C7C7C]">
                            <SvgIcon name="verified" />
                            <span >{{ user?.bio || user?.role }}</span>
                          </div>
                        </div>
                      </div>

                      <div
                        class="border-l-4 border-[#CB7C5D] dark:border-[#654D43] bg-[#FAF2EF] px-4 py-5 rounded-1 dark:bg-[#292929]"
                      >
                        <div
                          class="text-3.5 font-700 border-b-1 border-[#F2E8E4] dark:border-[#3E3E3E] text-[#2C2C2C] leading-4 pb-3 fx-cer gap-2 dark:text-white"
                        >
                          Achievement:
                        </div>
                        <div
                          class="text-3.5 font-400 leading-6 text-[#4D4846] py-3 dark:text-[#C6C6C6]"
                        >
                          <!-- {{ githubData.role_model?.achievement || 'GitHub Developer' }} -->
                           Former CHRO of a top 3 global fintech conglomerate, built a cross -
                          continental digital recruitment system in 5 years, led the acquisition of
                          100+ data science & financial analysis executives, driving 200% company
                          valuation growth.
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <img
              :src="user?.avatar || '@/assets/image/avator.png'"
              alt=""
              class="absolute top-[365px] right-[175px] w-[65px] h-[65px] rounded-full"
            />
          </div>
          <div class="fx-cer justify-between border-t dark:border-[#323232] h-[70px] mt-11">
            <img
              :src="isDark ? '/image/darklogo2.png' : '/image/newlogo1.png'"
              width="95"
              height="42"
              :alt="isDark ? 'DINQ dark logo' : 'DINQ logo'"
            />
            <div class="flex items-center gap-3" data-action-buttons>
              <!-- Download Button -->
              <button
                class="fx-cer bg-[#FFFFFF]/40 dark:bg-[#14141580] border border-gray-200 dark:border-[#27282D] rounded-full py-2 px-4 text-black dark:text-white gap-2 transition-all duration-200 select-none dark:shadow-lg disabled:opacity-70 disabled:cursor-not-allowed hover:bg-[#F5F5F5] dark:hover:bg-[#27282D] disabled:hover:bg-[#FFFFFF]/40 dark:disabled:hover:bg-[#14141580] min-h-[40px] cursor-pointer"
                :disabled="
                  downloadButtonState === 'downloading' || downloadButtonState === 'generating'
                "
                @click="handleDownload"
                style="backdrop-filter: blur(34px)"
              >
                <div
                  v-if="
                    downloadButtonState === 'downloading' || downloadButtonState === 'generating'
                  "
                  class="i-svg-spinners:3-dots-fade w-4 h-4 pointer-events-none"
                ></div>
                <div v-else class="i-material-symbols:download w-4 h-4 pointer-events-none"></div>
                <span class="text-sm font-medium pointer-events-none">{{
                  getDownloadButtonText()
                }}</span>
              </button>

              <!-- Share Button -->
              <ShareButton card-id="share-card-github" :is-dark="isDark" variant="transparent" />
            </div>
          </div>
        </div>
      </div>

      <button
        class="absolute top-4 right-4 p-2 rounded-full transition-colors bg-transparent dark:bg-[#141415] hover:bg-black/10 dark:hover:bg-white/10"
        @click="$emit('close')"
      >
        <div class="i-carbon:close text-xl text-gray-500"></div>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { defineProps, computed, ref } from 'vue'
  import html2canvas from 'html2canvas-pro'
  import SvgIcon from '../SvgIcon/index.vue'
  import GithubDonut from '../GithubDonut/index.vue'
  import ShareButton from '../ShareButton/index.vue'

  interface User {
    name: string
    avatar: string
    role: string
    login?: string
    bio?: string
  }

  interface Stats {
    repositories: number
    stars: number
    pullRequests: number
  }

  interface RoleModel {
    name: string
    avatar: string
    title: string
    achievement: string
  }

  interface InsightItem {
    label: string
    value: string | number
  }

  interface FeatureProject {
    name: string
    description: string
    url: string
    stargazerCount: number
    forkCount: number
    contributors: number
    used_by: number
    monthly_trending: number
  }

  interface ValuationLevel {
    salary_range: string | number[]
    level: string
    industry_ranking?: string | number
    growth_potential?: string
    reasoning?: string
    total_compensation?: string
  }

  interface MostValuablePR {
    title: string
    url: string
    repository: string
    impact: string
  }

  const props = defineProps<{
    show: boolean
    user?: User
    stats?: Stats
    income?: number
    roleModel?: RoleModel
    isDark?: boolean
    insightsItems?: InsightItem[]
    languages?: Record<string, number>
    languageTotal?: number
    featureProject?: FeatureProject
    additions?: number
    deletions?: number
    valuationLevel?: ValuationLevel
    workExperience?: number
    mostValuablePR?: MostValuablePR
    // 新增props用于接收OG图片相关状态
    ogImageBlob?: Blob | null
    ogImageGenerating?: boolean
    ogImageGenerated?: boolean
  }>()

  const isDownloading = ref(false)

  // 计算下载按钮状态
  const downloadButtonState = computed(() => {
    if (isDownloading.value) return 'downloading'
    if (props.ogImageGenerating) return 'generating'
    if (props.ogImageGenerated && props.ogImageBlob) return 'ready'
    return 'fallback'
  })

  // 获取下载按钮文本
  const getDownloadButtonText = () => {
    switch (downloadButtonState.value) {
      case 'downloading':
        return 'Downloading...'
      case 'generating':
        return 'Preparing...'
      case 'ready':
        return 'Download'
      case 'fallback':
        return 'Download'
      default:
        return 'Download'
    }
  }

  // 下载功能 - 优化版本，优先使用OG图片
  const handleDownload = async () => {
    if (isDownloading.value || !process.client) return

    isDownloading.value = true
    try {
      // 优先使用已生成的OG图片Blob
      if (props.ogImageGenerated && props.ogImageBlob) {
        console.log('Using cached GitHub OG image blob for download')
        await downloadFromBlob(props.ogImageBlob)
        return
      }

      // 降级到实时截图生成
      console.log('Falling back to real-time screenshot generation')
      await downloadFromScreenshot()
    } catch (error) {
      console.error('Download failed:', error)
      alert(
        'Screenshot failed. This might be due to CORS issues with external images. Please try again or contact support.'
      )
    } finally {
      isDownloading.value = false
    }
  }

  // 从Blob下载图片
  const downloadFromBlob = async (blob: Blob) => {
    const link = document.createElement('a')
    link.download = `dinq-github-analysis-${Date.now()}.png`
    link.href = URL.createObjectURL(blob)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(link.href) // 清理内存
  }

  // 等待图片加载完成的辅助函数
  const waitForImageLoad = (img: HTMLImageElement): Promise<void> => {
    return new Promise(resolve => {
      if (img.complete && img.naturalWidth > 0) {
        resolve()
      } else {
        const onLoad = () => {
          img.removeEventListener('load', onLoad)
          img.removeEventListener('error', onError)
          resolve()
        }
        const onError = () => {
          img.removeEventListener('load', onLoad)
          img.removeEventListener('error', onError)
          console.warn('Image failed to load:', img.src)
          resolve() // 即使失败也继续
        }
        img.addEventListener('load', onLoad)
        img.addEventListener('error', onError)
      }
    })
  }

  // 预加载QR码图片
  const preloadQRCode = (): Promise<void> => {
    return new Promise(resolve => {
      const img = new Image()
      img.onload = () => resolve()
      img.onerror = () => {
        console.warn('QR code failed to preload')
        resolve() // 即使失败也继续
      }
      img.src = '/image/qrcode.png'
    })
  }

  // 实时截图下载（降级方案）
  const downloadFromScreenshot = async () => {
    const elementToCapture = document.querySelector('[data-card-id="share-card-github"]')
    if (!elementToCapture) {
      throw new Error('GitHub share card element not found')
    }

    // 预加载QR码图片
    await preloadQRCode()

    // 等待所有图片加载完成
    const images = elementToCapture.getElementsByTagName('img')
    const imagePromises = [...images].map(img => waitForImageLoad(img))
    await Promise.all(imagePromises)

    // 等待字体加载完成
    if (document.fonts) {
      await document.fonts.ready
    }

    // 额外等待时间确保渲染完成
    await new Promise(resolve => setTimeout(resolve, 800))

    // 使用html2canvas-pro，它对现代CSS有更好的支持
    const canvas = await html2canvas(elementToCapture as HTMLElement, {
      backgroundColor: '#ffffff',
      scale: 2,
      useCORS: true,
      allowTaint: true,
      logging: false,
      imageTimeout: 15000,
      // html2canvas-pro有更好的SVG和CSS支持，所以简化配置
      foreignObjectRendering: false,
      // 确保元素完全在视图中
      scrollX: 0,
      scrollY: 0,
      // 在截图时替换按钮为版权信息
      onclone: clonedDoc => {
        const clonedElement = clonedDoc.querySelector('[data-card-id="share-card-github"]')
        if (clonedElement) {
          // 查找按钮容器并替换为版权信息和二维码
          const buttonContainer = clonedElement.querySelector('[data-action-buttons]')
          if (buttonContainer) {
            // 创建右下角信息容器（版权信息+二维码）
            const bottomRightContainer = clonedDoc.createElement('div')
            bottomRightContainer.style.cssText =
              'position: absolute; bottom: 16px; right: 16px; display: flex; align-items: center; gap: 8px; z-index: 10;'

            // 创建版权信息元素
            const copyrightDiv = clonedDoc.createElement('div')
            copyrightDiv.className = 'text-3.5 font-400'
            copyrightDiv.style.fontSize = '12px'
            copyrightDiv.style.color = props.isDark ? '#7A7A7A' : '#666'
            copyrightDiv.textContent = 'Copyright @ 2025 DINQ Inc. All rights reserved'

            // 创建二维码元素
            const qrCode = clonedDoc.createElement('img')
            qrCode.src = '/image/qrcode.png'
            qrCode.alt = 'QR Code'
            qrCode.style.cssText = 'width: 48px; height: 48px; flex-shrink: 0;'

            // 确保QR码图片能够正确加载
            qrCode.crossOrigin = 'anonymous'
            qrCode.loading = 'eager'

            // 将版权信息和二维码添加到右下角容器
            bottomRightContainer.appendChild(copyrightDiv)
            bottomRightContainer.appendChild(qrCode)

            // 替换按钮容器为右下角容器
            buttonContainer.parentNode?.replaceChild(bottomRightContainer, buttonContainer)
          }

          // 替换SVG图标为PNG图片以解决html2canvas兼容性问题
          const svgIconElements = clonedElement.querySelectorAll('svg.svg-icon')
          svgIconElements.forEach(svgEl => {
            const svgElement = svgEl as SVGElement
            const useElement = svgElement.querySelector('use')
            if (!useElement) return

            const iconId = useElement.getAttribute('xlink:href') || useElement.getAttribute('href')
            if (!iconId) return

            const imgElement = clonedDoc.createElement('img')

            // 根据图标ID确定要使用的图片
            if (iconId === '#icon-verified') {
              imgElement.src = '/image/sharecard/github-verify.png'
              imgElement.alt = 'verified'
              imgElement.className = 'w-4 h-4 mt-0.5 flex-shrink-0'
            } else if (iconId === '#icon-research') {
              imgElement.src = '/image/sharecard/overview.png'
              imgElement.alt = 'overview'
              imgElement.className = 'w-5 h-5'
            } else if (iconId === '#icon-add1') {
              imgElement.src = '/image/sharecard/additions.png'
              imgElement.alt = 'additions'
              imgElement.className = 'w-5 h-5'
            } else if (iconId === '#icon-trash-bin') {
              imgElement.src = '/image/sharecard/deletions.png'
              imgElement.alt = 'deletions'
              imgElement.className = 'w-5 h-5'
            } else if (iconId === '#icon-project') {
              imgElement.src = '/image/sharecard/highlight.png'
              imgElement.alt = 'highlight'
              imgElement.className = 'w-5 h-5'
            } else if (iconId === '#icon-stars') {
              imgElement.src = '/image/sharecard/stars.png'
              imgElement.alt = 'stars'
              imgElement.className = 'w-4 h-4'
            } else if (iconId === '#icon-forks') {
              imgElement.src = '/image/sharecard/forks.png'
              imgElement.alt = 'forks'
              imgElement.className = 'w-4 h-4'
            } else if (iconId === '#icon-growth') {
              imgElement.src = '/image/sharecard/marketvalue.png'
              imgElement.alt = 'market value'
              imgElement.className = 'w-5 h-5'
            } else if (iconId === '#icon-growth-investing') {
              imgElement.src = '/image/sharecard/yoe.png'
              imgElement.alt = 'yoe'
              imgElement.className = 'w-5 h-5'
            }

            // 替换SVG为IMG
            if (imgElement.src) {
              svgElement.parentNode?.replaceChild(imgElement, svgElement)
            }
          })

          // 替换头像的fallback路径
          const avatarImages = clonedElement.querySelectorAll(
            'img[src*="@/assets/image/avator.png"]'
          )
          avatarImages.forEach(img => {
            const imgEl = img as HTMLImageElement
            imgEl.src = '/image/avator.png'
          })

          // 修复深色模式下的边框颜色和高亮卡片样式
          const isDarkMode = props.isDark

          // 修复小卡片的背景图片显示
          const customBgCards = clonedElement.querySelectorAll('.custom-bg')
          customBgCards.forEach(card => {
            const cardEl = card as HTMLElement

            // 根据卡片内容确定背景图片类型
            const isAdditionsCard =
              cardEl.textContent?.includes('Additions') ||
              cardEl.querySelector('img[alt="additions"]')
            const isDeletionsCard =
              cardEl.textContent?.includes('Deletions') ||
              cardEl.querySelector('img[alt="deletions"]')
            const isMarketValueCard =
              cardEl.textContent?.includes('Market Value') ||
              cardEl.querySelector('img[alt="market value"]')
            const isYoECard =
              cardEl.textContent?.includes('YoE') || cardEl.querySelector('img[alt="yoe"]')

            // 强制设置背景图片，使用!important确保优先级
            if (isDarkMode) {
              // 深色模式统一使用深色背景
              cardEl.style.setProperty(
                'background-image',
                'url(/image/sharecard/Group2xdark.png)',
                'important'
              )
            } else {
              // 亮色模式根据卡片类型使用不同背景
              if (isAdditionsCard || isMarketValueCard) {
                // Additions和Market Value使用Group2x1.png
                cardEl.style.setProperty(
                  'background-image',
                  'url(/image/sharecard/Group2x1.png)',
                  'important'
                )
              } else if (isDeletionsCard || isYoECard) {
                // Deletions和YoE使用Group2x.png
                cardEl.style.setProperty(
                  'background-image',
                  'url(/image/sharecard/Group2x.png)',
                  'important'
                )
              }
            }

            // 确保其他背景属性正确设置
            cardEl.style.setProperty('background-repeat', 'no-repeat', 'important')
            cardEl.style.setProperty('background-size', 'contain', 'important')
            cardEl.style.setProperty('background-position', 'left top', 'important')

            // 修复文字颜色以匹配背景图片
            const textElements = cardEl.querySelectorAll('[class*="text-[#"]')
            textElements.forEach(textEl => {
              const textElement = textEl as HTMLElement
              if (isDarkMode) {
                // 深色模式下的文字颜色
                if (isAdditionsCard || isMarketValueCard) {
                  // 蓝色系卡片 - 深色模式使用较亮的蓝色
                  if (
                    textElement.classList.contains('text-[#5F6D94]') ||
                    textElement.classList.contains('dark:text-[#A5AEC6]')
                  ) {
                    textElement.style.setProperty('color', '#A5AEC6', 'important')
                  }
                } else if (isDeletionsCard || isYoECard) {
                  // 橙色系卡片 - 深色模式使用较亮的橙色
                  if (
                    textElement.classList.contains('text-[#CB7C5D]') ||
                    textElement.classList.contains('dark:text-[#B28383]')
                  ) {
                    textElement.style.setProperty('color', '#B28383', 'important')
                  }
                }
              } else {
                // 浅色模式下的文字颜色
                if (isAdditionsCard || isMarketValueCard) {
                  // 蓝色系卡片 - 确保文字颜色正确
                  if (textElement.classList.contains('text-[#5F6D94]')) {
                    textElement.style.setProperty('color', '#5F6D94', 'important')
                  }
                } else if (isDeletionsCard || isYoECard) {
                  // 橙色系卡片 - 确保文字颜色正确
                  if (textElement.classList.contains('text-[#CB7C5D]')) {
                    textElement.style.setProperty('color', '#CB7C5D', 'important')
                  }
                }
              }
            })
          })

          // 修复毛玻璃效果卡片的背景和样式
          const glassCards = clonedElement.querySelectorAll(
            '[style*="backdrop-filter: blur(14px)"]'
          )
          glassCards.forEach(card => {
            const cardEl = card as HTMLElement
            if (isDarkMode) {
              // 深色模式：增强背景不透明度以模拟毛玻璃效果
              cardEl.style.backgroundColor = 'rgba(20, 20, 21, 0.85)'
              cardEl.style.backdropFilter = 'none' // 移除backdrop-filter，因为html2canvas不支持
              cardEl.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.3)'
            } else {
              // 亮色模式：增强背景不透明度以模拟毛玻璃效果
              cardEl.style.backgroundColor = 'rgba(255, 255, 255, 0.85)'
              cardEl.style.backdropFilter = 'none' // 移除backdrop-filter，因为html2canvas不支持
              cardEl.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.1)'
            }
          })

          // 修复高亮卡片的背景颜色和边框（适用于所有模式）
          const highlightCards = clonedElement.querySelectorAll('.border-l-4')
          highlightCards.forEach(card => {
            const cardEl = card as HTMLElement
            if (isDarkMode) {
              // 深色模式样式
              cardEl.style.backgroundColor = '#222222'
              cardEl.style.borderLeftColor = '#654D43'
              cardEl.style.borderLeftWidth = '4px'
              cardEl.style.borderLeftStyle = 'solid'
            } else {
              // 亮色模式样式
              cardEl.style.backgroundColor = '#FAF2EF'
              cardEl.style.borderLeftColor = '#CB7C5D'
              cardEl.style.borderLeftWidth = '4px'
              cardEl.style.borderLeftStyle = 'solid'
            }

            // 修复高亮卡片内的分割线颜色
            const dividers = cardEl.querySelectorAll('.border-b-1')
            dividers.forEach(divider => {
              const dividerEl = divider as HTMLElement
              if (isDarkMode) {
                dividerEl.style.borderBottomColor = '#3E3E3E'
              } else {
                dividerEl.style.borderBottomColor = '#F2E8E4'
              }
            })
          })

          if (isDarkMode) {
            // 修复所有卡片的边框颜色
            const cardElements = clonedElement.querySelectorAll('.border-gray-200')
            cardElements.forEach(card => {
              ;(card as HTMLElement).style.borderColor = '#27282D'
            })

            // 修复主卡片的边框颜色
            const mainCard = clonedElement.querySelector('[data-card-id="share-card-github"] > div')
            if (mainCard) {
              ;(mainCard as HTMLElement).style.borderColor = '#27282D'
            }

            // 修复卡片标题文字颜色
            const titleElements = clonedElement.querySelectorAll('.text-black')
            titleElements.forEach(title => {
              ;(title as HTMLElement).style.color = '#FAF9F5'
            })
          }
        }
      },
    })

    // 创建下载链接
    const link = document.createElement('a')
    link.download = `dinq-github-analysis-${Date.now()}.png`
    link.href = canvas.toDataURL('image/png', 1.0)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // 为GithubDonut组件提供语言数据
  const languageData = computed(() => props.languages || {})
  const languageTotal = computed(() => props.languageTotal || 0)

  // 格式化数字，超过1K时显示为K单位，超过1M时显示为M单位
  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(0) + 'K'
    }
    return num.toString()
  }

  // 格式化薪资范围，自动转换为K/M单位
  const formatSalaryRange = (salaryRange: string | number[]): string => {
    // 如果是数组格式，直接处理
    if (Array.isArray(salaryRange)) {
      const formatSalaryValue = (value: number): string => {
        if (value >= 1000000) {
          return (value / 1000000).toFixed(1) + 'M'
        }
        if (value >= 1000) {
          return value / 1000 + 'K'
        }
        return value.toString()
      }

      return `$${formatSalaryValue(salaryRange[0])} ~ $${formatSalaryValue(salaryRange[1])}`
    }

    // 如果是字符串格式，使用原来的逻辑
    const rangeMatch = salaryRange.match(
      /\$(\d+(?:,\d{3})*(?:[KkMm])?)\s*-\s*\$(\d+(?:,\d{3})*(?:[KkMm])?)/
    )

    if (rangeMatch) {
      const [, minStr, maxStr] = rangeMatch

      const formatSalaryValue = (value: string): string => {
        // 如果已经包含K或M，直接返回
        if (value.includes('K') || value.includes('k')) {
          return value.toUpperCase()
        }
        if (value.includes('M') || value.includes('m')) {
          return value.toUpperCase()
        }

        // 移除逗号并转换为数字
        const num = parseInt(value.replace(/,/g, ''))

        if (num >= 1000000) {
          return (num / 1000000).toFixed(1) + 'M'
        }
        if (num >= 1000) {
          return num / 1000 + 'K'
        }

        return num.toString()
      }

      return `$${formatSalaryValue(minStr)} ~ $${formatSalaryValue(maxStr)}`
    }

    // 如果格式不匹配，返回原始值
    return salaryRange
  }

  // 格式化薪资最低值，只返回数字部分（不包含$和K/M）
  const formatMinSalary = (salaryRange: string | number[]): string => {
    // 如果是数组格式，直接处理
    if (Array.isArray(salaryRange)) {
      const minValue = salaryRange[0]
      if (minValue >= 1000000) {
        return (minValue / 1000000).toFixed(1)
      }
      if (minValue >= 1000) {
        return (minValue / 1000).toString()
      }
      return minValue.toString()
    }

    // 如果是字符串格式，提取最低值
    const rangeMatch = salaryRange.match(/\$(\d+(?:,\d{3})*(?:[KkMm])?)/)

    if (rangeMatch) {
      const minStr = rangeMatch[1]

      // 如果已经包含M，移除M并返回数字
      if (minStr.includes('M') || minStr.includes('m')) {
        return minStr.replace(/[Mm]/g, '')
      }

      // 如果已经包含K，移除K并返回数字
      if (minStr.includes('K') || minStr.includes('k')) {
        return minStr.replace(/[Kk]/g, '')
      }

      // 移除逗号并转换为数字，然后转换为K/M单位
      const num = parseInt(minStr.replace(/,/g, ''))

      if (num >= 1000000) {
        return (num / 1000000).toFixed(1)
      }
      if (num >= 1000) {
        return (num / 1000).toString()
      }

      return num.toString()
    }

    // 如果格式不匹配，返回默认值
    return '120'
  }

  // 格式化薪资最低值，包含K/M后缀
  const formatMinSalaryWithSuffix = (salaryRange: string | number[]): string => {
    // 如果是数组格式，直接处理
    if (Array.isArray(salaryRange)) {
      const minValue = salaryRange[0]
      if (minValue >= 1000000) {
        return (minValue / 1000000).toFixed(1) + 'M'
      }
      if (minValue >= 1000) {
        return (minValue / 1000).toString() + 'K'
      }
      return minValue.toString()
    }

    // 如果是字符串格式，提取最低值
    const rangeMatch = salaryRange.match(/\$(\d+(?:\.\d+)?(?:,\d{3})*(?:[KkMm])?)/)

    if (rangeMatch) {
      const minStr = rangeMatch[1]

      // 如果已经包含M，直接返回
      if (minStr.includes('M') || minStr.includes('m')) {
        return minStr.replace(/[Mm]/g, 'M')
      }

      // 如果已经包含K，直接返回
      if (minStr.includes('K') || minStr.includes('k')) {
        return minStr.replace(/[Kk]/g, 'K')
      }

      // 移除逗号并转换为数字，然后转换为K/M单位
      const num = parseInt(minStr.replace(/,/g, ''))

      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M'
      }
      if (num >= 1000) {
        return (num / 1000).toString() + 'K'
      }

      return num.toString()
    }

    // 如果格式不匹配，返回默认值
    return '120K'
  }
</script>

<style scoped>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .animate-spin {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>
